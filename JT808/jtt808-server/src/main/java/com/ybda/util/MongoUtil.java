package com.ybda.util;

import com.ybda.model.entity.DeviceLocation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

/**
 * MongoDB操作工具类
 * 专门用于设备位置信息的数据库操作
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class MongoUtil {

    private final MongoTemplate mongoTemplate;

    /**
     * 保存设备位置信息
     * @param location 位置信息
     * @return 保存后的完整位置信息（包含MongoDB自动生成的ID）
     */
    public DeviceLocation saveLocation(DeviceLocation location) {
        try {
            mongoTemplate.save(location);  // save后，location对象会自动获得ID
            log.debug("保存位置信息成功: deviceId={}, time={}, id={}",
                location.getDeviceId(), location.getDeviceTime(), location.getId());
            return location;  // 返回包含ID的完整对象
        } catch (Exception e) {
            log.error("保存位置信息失败: deviceId={}, error={}",
                location.getDeviceId(), e.getMessage(), e);
            throw new RuntimeException("位置信息保存失败", e);
        }
    }

    /**
     * 批量保存设备位置信息
     * @param locations 位置信息列表
     * @return 保存后的位置信息列表（包含MongoDB自动生成的ID）
     */
    public List<DeviceLocation> batchSaveLocations(List<DeviceLocation> locations) {
        try {
            mongoTemplate.insertAll(locations);  // insertAll后，locations中的对象会自动获得ID
            log.debug("批量保存位置信息成功: count={}", locations.size());
            return locations;  // 返回包含ID的完整对象列表
        } catch (Exception e) {
            log.error("批量保存位置信息失败: count={}, error={}",
                locations.size(), e.getMessage(), e);
            throw new RuntimeException("批量位置信息保存失败", e);
        }
    }

    /**
     * 查询设备最新位置信息
     * @param deviceId 设备ID
     * @return 最新位置信息
     */
    public DeviceLocation getLatestLocation(String deviceId) {
        try {
            Query query = new Query(Criteria.where("device_id").is(deviceId))
                .with(Sort.by(Sort.Direction.DESC, "device_time"))
                .limit(1);
            
            DeviceLocation location = mongoTemplate.findOne(query, DeviceLocation.class);
            log.debug("查询最新位置: deviceId={}, found={}", deviceId, location != null);
            return location;
        } catch (Exception e) {
            log.error("查询最新位置失败: deviceId={}, error={}", deviceId, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 查询设备指定时间范围内的位置信息
     * @param deviceId 设备ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param limit 限制数量（0表示不限制）
     * @return 位置信息列表
     */
    public List<DeviceLocation> getLocationsByTimeRange(String deviceId, 
                                                       LocalDateTime startTime, 
                                                       LocalDateTime endTime, 
                                                       int limit) {
        try {
            Criteria criteria = Criteria.where("device_id").is(deviceId)
                    .and("device_time").gte(startTime).lte(endTime);


            Query query = new Query(criteria)
                .with(Sort.by(Sort.Direction.DESC, "device_time"));
            
            if (limit > 0) {
                query.limit(limit);
            }
            
            List<DeviceLocation> locations = mongoTemplate.find(query, DeviceLocation.class);
            log.debug("查询时间范围位置: deviceId={}, start={}, end={}, count={}", 
                deviceId, startTime, endTime, locations.size());
            return locations;
        } catch (Exception e) {
            log.error("查询时间范围位置失败: deviceId={}, error={}", deviceId, e.getMessage(), e);
            return List.of();
        }
    }

    /**
     * 查询设备最近N条位置记录
     * @param deviceId 设备ID
     * @param count 记录数量
     * @return 位置信息列表
     */
    public List<DeviceLocation> getRecentLocations(String deviceId, int count) {
        try {
            Query query = new Query(Criteria.where("device_id").is(deviceId))
                .with(Sort.by(Sort.Direction.DESC, "device_time"))
                .limit(count);
            
            List<DeviceLocation> locations = mongoTemplate.find(query, DeviceLocation.class);
            log.debug("查询最近位置: deviceId={}, count={}, found={}", 
                deviceId, count, locations.size());
            return locations;
        } catch (Exception e) {
            log.error("查询最近位置失败: deviceId={}, error={}", deviceId, e.getMessage(), e);
            return List.of();
        }
    }

    /**
     * 查询指定区域内的设备位置
     * @param minLng 最小经度
     * @param maxLng 最大经度
     * @param minLat 最小纬度
     * @param maxLat 最大纬度
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 位置信息列表
     */
    public List<DeviceLocation> getLocationsByArea(double minLng, double maxLng, 
                                                  double minLat, double maxLat,
                                                  LocalDateTime startTime, 
                                                  LocalDateTime endTime) {
        try {
            Criteria criteria = new Criteria();
            criteria.and("lng").gte(minLng).lte(maxLng);
            criteria.and("lat").gte(minLat).lte(maxLat);
            
            if (startTime != null) {
                criteria.and("device_time").gte(startTime);
            }
            if (endTime != null) {
                criteria.and("device_time").lte(endTime);
            }
            
            Query query = new Query(criteria)
                .with(Sort.by(Sort.Direction.DESC, "device_time"));
            
            List<DeviceLocation> locations = mongoTemplate.find(query, DeviceLocation.class);
            log.debug("查询区域位置: area=[{},{},{},{}], count={}", 
                minLng, maxLng, minLat, maxLat, locations.size());
            return locations;
        } catch (Exception e) {
            log.error("查询区域位置失败: error={}", e.getMessage(), e);
            return List.of();
        }
    }

    /**
     * 删除指定时间之前的位置数据（数据清理）
     * @param beforeTime 时间点
     * @return 删除的记录数
     */
    public long deleteLocationsBefore(LocalDateTime beforeTime) {
        try {
            Query query = new Query(Criteria.where("device_time").lt(beforeTime));
            long deletedCount = mongoTemplate.remove(query, DeviceLocation.class).getDeletedCount();
            log.info("清理历史位置数据: beforeTime={}, deletedCount={}", beforeTime, deletedCount);
            return deletedCount;
        } catch (Exception e) {
            log.error("清理历史位置数据失败: beforeTime={}, error={}", beforeTime, e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 统计设备位置记录数量
     * @param deviceId 设备ID（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 记录数量
     */
    public long countLocations(String deviceId, LocalDateTime startTime, LocalDateTime endTime) {
        try {
            Criteria criteria = new Criteria();
            
            if (deviceId != null && !deviceId.isEmpty()) {
                criteria.and("device_id").is(deviceId);
            }
            if (startTime != null) {
                criteria.and("device_time").gte(startTime);
            }
            if (endTime != null) {
                criteria.and("device_time").lte(endTime);
            }
            
            Query query = new Query(criteria);
            long count = mongoTemplate.count(query, DeviceLocation.class);
            log.debug("统计位置记录: deviceId={}, count={}", deviceId, count);
            return count;
        } catch (Exception e) {
            log.error("统计位置记录失败: deviceId={}, error={}", deviceId, e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 检查MongoDB连接状态
     * @return 是否连接正常
     */
    public boolean checkConnection() {
        try {
            mongoTemplate.getCollection("device_location").estimatedDocumentCount();
            return true;
        } catch (Exception e) {
            log.error("MongoDB连接检查失败: {}", e.getMessage());
            return false;
        }
    }
}
