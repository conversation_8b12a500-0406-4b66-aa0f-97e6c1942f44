package com.ybda.service.impl;

import com.ybda.model.entity.Device;
import com.ybda.model.entity.DeviceLocation;
import com.ybda.protocol.t808.T0200;
import com.ybda.service.DeviceService;
import com.ybda.service.LocationCacheService;
import com.ybda.service.LocationService;
import com.ybda.util.MongoUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 位置信息服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LocationServiceImpl implements LocationService {

    private final MongoUtil mongoUtil;
    private final DeviceService deviceService;
    private final LocationCacheService locationCacheService;

    @Override
    @Async
    public void saveLocation(T0200 t0200, String deviceId, String mobileNo, String plateNo) {
        try {
            DeviceLocation location = convertToDeviceLocation(t0200, deviceId, mobileNo, plateNo);
            // 异步逆地理编码（可选）
            if (location.getLng() != null && location.getLat() != null) {
                String address = reverseGeocode(location.getLng(), location.getLat());
                location.setAddress(address);
            }
            // 保存到MongoDB并获取包含ID的完整对象
            DeviceLocation savedLocation = mongoUtil.saveLocation(location);

            // 更新设备的实时位置（内存中）并缓存完整对象
            updateDeviceRealTimeLocationWithSavedData(deviceId, t0200, savedLocation);
            
            log.debug("位置信息保存成功: deviceId={}, time={}, lng={}, lat={}", 
                deviceId, t0200.getDeviceTime(), t0200.getLng(), t0200.getLat());
                
        } catch (Exception e) {
            log.error("保存位置信息异常: deviceId={}, error={}", deviceId, e.getMessage(), e);
        }
    }

    @Override
    @Async
    public void batchSaveLocations(List<T0200> t0200List, String deviceId, String mobileNo, String plateNo) {
        try {
            List<DeviceLocation> locations = t0200List.stream()
                .map(t0200 -> convertToDeviceLocation(t0200, deviceId, mobileNo, plateNo))
                .collect(Collectors.toList());

            // 批量保存并获取包含ID的完整对象列表
            List<DeviceLocation> savedLocations = mongoUtil.batchSaveLocations(locations);

            // 更新设备的最新位置（取最后一个已保存的对象）
            if (!savedLocations.isEmpty()) {
                DeviceLocation latestSavedLocation = savedLocations.get(savedLocations.size() - 1);
                T0200 latestT0200 = t0200List.get(t0200List.size() - 1);
                updateDeviceRealTimeLocationWithSavedData(deviceId, latestT0200, latestSavedLocation);
            }
            log.debug("批量位置信息保存成功: deviceId={}, count={}", deviceId, t0200List.size());
        } catch (Exception e) {
            log.error("批量保存位置信息异常: deviceId={}, count={}, error={}",
                deviceId, t0200List.size(), e.getMessage(), e);
        }
    }

    @Override
    public DeviceLocation getLatestLocation(String deviceId) {
        // 优先从Redis缓存获取
        DeviceLocation cachedLocation = locationCacheService.getLatestLocationFromCache(deviceId);
        if (cachedLocation != null) {
            log.debug("从缓存获取设备最新位置: deviceId={}", deviceId);
            return cachedLocation;
        }
        // 缓存未命中，从MongoDB查询
        DeviceLocation location = mongoUtil.getLatestLocation(deviceId);
        if (location != null) {
            // 查询到数据后缓存到Redis
            locationCacheService.cacheLatestLocation(deviceId, location);
            log.debug("从数据库获取设备最新位置并缓存: deviceId={}", deviceId);
        }
        return location;
    }

    @Override
    public List<DeviceLocation> getDeviceTrack(String deviceId, LocalDateTime startTime, 
                                              LocalDateTime endTime, int limit) {
        return mongoUtil.getLocationsByTimeRange(deviceId, startTime, endTime, limit);
    }

    @Override
    public List<DeviceLocation> getRecentTrack(String deviceId, int count) {
        return mongoUtil.getRecentLocations(deviceId, count);
    }

    @Override
    public List<DeviceLocation> getLocationsByArea(double minLng, double maxLng, 
                                                  double minLat, double maxLat,
                                                  LocalDateTime startTime, LocalDateTime endTime) {
        return mongoUtil.getLocationsByArea(minLng, maxLng, minLat, maxLat, startTime, endTime);
    }

    @Override
    public List<DeviceLocation> getAllOnlineDeviceLocations() {
        try {
            // 获取所有在线设备
            List<Device> onlineDevices = deviceService.getEnabledDevices().stream()
                .filter(device -> device.getIsOnline() != null && device.getIsOnline() == 1)
                .collect(Collectors.toList());

            List<DeviceLocation> locations = new ArrayList<>();

            // 获取每个在线设备的最新位置
            for (Device device : onlineDevices) {
                DeviceLocation location = getLatestLocation(device.getDeviceId());
                if (location != null) {
                    locations.add(location);
                }
            }

            log.debug("获取在线设备位置: onlineCount={}, locationCount={}",
                onlineDevices.size(), locations.size());
            return locations;

        } catch (Exception e) {
            log.error("获取在线设备位置异常: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<DeviceLocation> getAllEnabledDeviceLocations() {
        try {
            // 获取所有启用状态的设备（包括在线和离线）
            List<Device> enabledDevices = deviceService.getEnabledDevices();

            List<DeviceLocation> locations = new ArrayList<>();

            // 获取每个启用设备的最新位置
            for (Device device : enabledDevices) {
                DeviceLocation location = getLatestLocation(device.getDeviceId());
                if (location != null) {
                    locations.add(location);
                }
            }

            log.debug("获取启用设备位置: enabledCount={}, locationCount={}",
                enabledDevices.size(), locations.size());
            return locations;

        } catch (Exception e) {
            log.error("获取启用设备位置异常: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public long countLocationRecords(String deviceId, LocalDateTime startTime, LocalDateTime endTime) {
        return mongoUtil.countLocations(deviceId, startTime, endTime);
    }

    @Override
    public long cleanHistoryLocations(LocalDateTime beforeTime) {
        return mongoUtil.deleteLocationsBefore(beforeTime);
    }

    @Override
    public DeviceLocation getLatestLocationFromCache(String deviceId) {
        return locationCacheService.getLatestLocationFromCache(deviceId);
    }

    @Override
    public Map<String, DeviceLocation> getAllLatestLocationsFromCache() {
        return locationCacheService.getAllLatestLocationsFromCache();
    }

    @Override
    public String reverseGeocode(double lng, double lat) {
        try {
            // TODO: 集成百度地图或高德地图逆地理编码API
            // 这里先返回简单的坐标信息，后续可以集成真实的地理编码服务
            return String.format("位置: %.6f,%.6f", lng, lat);
        } catch (Exception e) {
            log.warn("逆地理编码失败: lng={}, lat={}, error={}", lng, lat, e.getMessage());
            return null;
        }
    }

    /**
     * 将T0200转换为DeviceLocation
     */
    private DeviceLocation convertToDeviceLocation(T0200 t0200, String deviceId, String mobileNo, String plateNo) {
        DeviceLocation location = new DeviceLocation();
        
        // 基本信息
        location.setDeviceId(deviceId);
        location.setMobileNo(mobileNo);
        location.setPlateNo(plateNo);
        // 位置信息
        location.setWarnBit(t0200.getWarnBit());
        location.setStatusBit(t0200.getStatusBit());
//        location.setLatitude(t0200.getLatitude());
//        location.setLongitude(t0200.getLongitude());
        location.setLat(t0200.getLat());
        location.setLng(t0200.getLng());
        location.setAltitude(t0200.getAltitude());
//        location.setSpeed(t0200.getSpeed());
        location.setSpeedKph(t0200.getSpeedKph());
        location.setDirection(t0200.getDirection());
        // 设备时间（截断到秒级精度，去除纳秒部分）
        if (t0200.getDeviceTime() != null) {
            LocalDateTime deviceTime = t0200.getDeviceTime().withNano(0);
            location.setDeviceTime(deviceTime);
        }
        // 处理位置附加信息，转换为可读格式
        if (t0200.getAttributes() != null && !t0200.getAttributes().isEmpty()) {
            Map<String, Object> processedAttributes = processAttributes(t0200.getAttributes());
            location.setAttributes(processedAttributes);
        }
        // 状态判断
        location.setIsValid((t0200.getStatusBit() & 0x02) == 0); // 位1为0表示GPS定位有效
        // 从附加信息中提取卫星数量（如果有）
        if (t0200.getAttributes() != null) {
            Integer satelliteCount = (Integer) t0200.getAttributes().get(0x31);
            if (satelliteCount == null) {
                // 也尝试从0x04键获取（某些设备可能使用这个键）
                satelliteCount = (Integer) t0200.getAttributes().get(0x04);
            }
            location.setSatelliteCount(satelliteCount);
        }
        // 时间信息
        location.setReceiveTime(LocalDateTime.now());
        location.setCreateTime(LocalDateTime.now());
//        try {
//            SearchHttpAK.requestGet(location.getLat().toString(), location.getLng().toString());
//        } catch (Exception e) {
//            throw new RuntimeException(e);
//        }
        return location;
    }

    /**
     * 使用已保存的位置数据更新设备实时位置（避免重复查询数据库）
     */
    private void updateDeviceRealTimeLocationWithSavedData(String deviceId, T0200 t0200, DeviceLocation savedLocation) {
        try {
            Device device = deviceService.getDeviceByDeviceId(deviceId);
            if (device != null) {
                // 更新内存中的设备位置
                device.updateLocation(t0200);

                // 直接缓存已保存的完整位置对象到Redis（包含ID）
                locationCacheService.cacheLatestLocation(deviceId, savedLocation);

                log.debug("更新设备实时位置(内存+缓存): deviceId={}, time={}, id={}",
                        deviceId, t0200.getDeviceTime(), savedLocation.getId());
            }
        } catch (Exception e) {
            log.warn("更新设备实时位置失败: deviceId={}, error={}", deviceId, e.getMessage());
        }
    }

    /**
     * 处理位置附加信息，转换为可读的键值对格式
     * @param attributes 原始附加信息
     * @return 处理后的附加信息
     */
    private Map<String, Object> processAttributes(Map<Integer, Object> attributes) {
        Map<String, Object> processedAttributes = new HashMap<>();

        for (Map.Entry<Integer, Object> entry : attributes.entrySet()) {
            Integer key = entry.getKey();
            Object value = entry.getValue();
            String keyName = getAttributeKeyName(key);

            // 根据不同的附加信息类型进行处理
            Object processedValue = processAttributeValue(key, value);
            processedAttributes.put(keyName, processedValue);
        }

        return processedAttributes;
    }

    /**
     * 获取附加信息键的名称
     */
    private String getAttributeKeyName(Integer key) {
        switch (key) {
            case 0x01: return "里程";
            case 0x02: return "油量";
            case 0x03: return "行驶记录功能获取的速度";
            case 0x04: return "需要人工确认报警事件的ID";
            case 0x05: return "胎压";
            case 0x06: return "车厢温度";
            case 0x11: return "超速报警附加信息";
            case 0x12: return "进出区域/路线报警附加信息";
            case 0x13: return "路段行驶时间不足/过长报警附加信息";
            case 0x25: return "扩展车辆信号状态位";
            case 0x2A: return "IO状态位";
            case 0x2B: return "模拟量";
            case 0x30: return "无线通信网络信号强度";
            case 0x31: return "GNSS定位卫星数";
            // 用户自定义透传数据范围 0xF0~0xFF
            case 0xF0: case 0xF1: case 0xF2: case 0xF3: case 0xF4: case 0xF5: case 0xF6: case 0xF7:
            case 0xF8: case 0xF9: case 0xFA: case 0xFB: case 0xFC: case 0xFD: case 0xFE: case 0xFF:
                return "用户自定义数据_" + String.format("0x%02X", key);
            default: return "附加信息_" + String.format("0x%02X", key);
        }
    }

    /**
     * 处理附加信息的值
     */
    private Object processAttributeValue(Integer key, Object value) {
        // 处理用户自定义透传数据（0xF0~0xFF）
        if (key >= 0xF0 && key <= 0xFF) {
            return processBinaryData(value);
        }

        switch (key) {
            case 0x01: // 里程，单位为1/10km
                if (value instanceof Long) {
                    return ((Long) value) / 10.0 + "km";
                } else if (value instanceof Integer) {
                    return ((Integer) value) / 10.0 + "km";
                }
                break;
            case 0x02: // 油量，单位为1/10L
                if (value instanceof Integer) {
                    return ((Integer) value) / 10.0 + "L";
                }
                break;
            case 0x03: // 行驶记录功能获取的速度，单位为1/10km/h
                if (value instanceof Integer) {
                    return ((Integer) value) / 10.0 + "km/h";
                }
                break;
            case 0x30: // 无线通信网络信号强度
                if (value instanceof Integer) {
                    return value + "dBm";
                }
                break;
            case 0x31: // GNSS定位卫星数
                if (value instanceof Integer) {
                    return value + "颗";
                }
                break;
            default:
                return value;
        }
        return value;
    }

    /**
     * 处理二进制数据
     */
    private Object processBinaryData(Object value) {
        if (value instanceof String) {
            String base64Data = (String) value;
            try {
                // 尝试解码Base64数据
                byte[] decodedData = Base64.getDecoder().decode(base64Data);

                // 创建一个包含原始Base64和解码信息的对象
                Map<String, Object> binaryInfo = new HashMap<>();
                binaryInfo.put("base64", base64Data);
                binaryInfo.put("length", decodedData.length);
                binaryInfo.put("hex", bytesToHex(decodedData));

                // 如果数据较短，尝试解析为可读文本
                if (decodedData.length <= 32) {
                    try {
                        String text = new String(decodedData, "UTF-8");
                        // 检查是否为可打印字符
                        if (isPrintableText(text)) {
                            binaryInfo.put("text", text);
                        }
                    } catch (Exception ignored) {
                        // 忽略解码异常
                    }
                }

                return binaryInfo;
            } catch (Exception e) {
                // 如果不是有效的Base64，直接返回原值
                return value;
            }
        }
        return value;
    }

    /**
     * 将字节数组转换为十六进制字符串
     */
    private String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02X ", b));
        }
        return result.toString().trim();
    }

    /**
     * 检查文本是否为可打印字符
     */
    private boolean isPrintableText(String text) {
        for (char c : text.toCharArray()) {
            if (c < 32 || c > 126) { // ASCII可打印字符范围
                return false;
            }
        }
        return true;
    }
}
