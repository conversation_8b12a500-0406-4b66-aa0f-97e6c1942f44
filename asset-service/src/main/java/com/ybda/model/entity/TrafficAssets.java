package com.ybda.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 交通资产表
 */
@Data
@TableName(value = "traffic_assets")
public class TrafficAssets {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 资产唯一标识
     */
    @TableField(value = "asset_id")
    private String assetId;

    /**
     * 资产类型：ground_marking/overhead_sign/traffic_light/barrier
     */
    @TableField(value = "`type`")
    private String type;

    /**
     * 资产名称
     */
    @TableField(value = "`name`")
    private String name;

    /**
     * 资产纬度
     */
    @TableField(value = "latitude")
    private BigDecimal latitude;

    /**
     * 资产经度
     */
    @TableField(value = "longitude")
    private BigDecimal longitude;

    /**
     * 首次检测时间
     */
    @TableField(value = "first_detected_time")
    private Date firstDetectedTime;

    /**
     * 最后检测时间
     */
    @TableField(value = "last_detected_time")
    private Date lastDetectedTime;

    /**
     * 检测次数
     */
    @TableField(value = "detection_count")
    private Integer detectionCount;

    /**
     * 资产状态
     */
    @TableField(value = "`status`")
    private String status;

    /**
     * 资产是否存在（0存在1存在）
     */
    @TableField(value = "available")
    private Integer available;

    /**
     * 创建时间
     */
    @TableField(value = "created_time")
    private Date createdTime;

    /**
     * 更新时间
     */
    @TableField(value = "updated_time")
    private Date updatedTime;
}