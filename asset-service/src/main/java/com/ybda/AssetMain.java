package com.ybda;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.scheduling.annotation.EnableScheduling;

@Slf4j
@EnableCaching
@EnableScheduling
@EnableDiscoveryClient
@SpringBootApplication
public class AssetMain {
        public static void main(String[] args) {
            SpringApplication.run(AssetMain.class, args);
        }
    }
