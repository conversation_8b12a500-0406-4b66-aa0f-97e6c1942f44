<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ybda.mapper.TrafficAssetsMapper">
  <resultMap id="BaseResultMap" type="com.ybda.model.entity.TrafficAssets">
    <!--@mbg.generated-->
    <!--@Table traffic_assets-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="asset_id" jdbcType="VARCHAR" property="assetId" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="latitude" jdbcType="DECIMAL" property="latitude" />
    <result column="longitude" jdbcType="DECIMAL" property="longitude" />
    <result column="first_detected_time" jdbcType="TIMESTAMP" property="firstDetectedTime" />
    <result column="last_detected_time" jdbcType="TIMESTAMP" property="lastDetectedTime" />
    <result column="detection_count" jdbcType="INTEGER" property="detectionCount" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="available" jdbcType="INTEGER" property="available" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, asset_id, `type`, `name`, latitude, longitude, first_detected_time, last_detected_time, 
    detection_count, `status`, available, created_time, updated_time
  </sql>
</mapper>